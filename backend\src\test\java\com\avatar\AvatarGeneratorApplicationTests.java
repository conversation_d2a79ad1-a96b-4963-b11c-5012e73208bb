package com.avatar;

import com.avatar.entity.Avatar;
import com.avatar.repository.AvatarRepository;
import com.avatar.service.AvatarService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
class AvatarGeneratorApplicationTests {

    @Autowired
    private AvatarService avatarService;

    @Autowired
    private AvatarRepository avatarRepository;

    @Test
    void contextLoads() {
        assertNotNull(avatarService);
        assertNotNull(avatarRepository);
    }

    @Test
    void testAvatarStats() {
        AvatarService.AvatarStats stats = avatarService.getAvatarStats();
        assertNotNull(stats);
        assertTrue(stats.getTotalCount() >= 0);
        assertTrue(stats.getUsedCount() >= 0);
        assertTrue(stats.getUnusedCount() >= 0);
    }

    @Test
    void testRandomAvatarWhenEmpty() {
        // 当没有头像时，应该返回空
        Optional<Avatar> avatar = avatarService.getRandomAvatar();
        assertFalse(avatar.isPresent());
    }

    @Test
    void testCreateAndRetrieveAvatar() {
        // 创建测试头像
        Avatar testAvatar = new Avatar(
            "test.jpg",
            "test-uuid.jpg", 
            "/test/path/test-uuid.jpg",
            1024L,
            "image/jpeg"
        );
        
        Avatar saved = avatarRepository.save(testAvatar);
        assertNotNull(saved.getId());
        assertFalse(saved.getIsUsed());
        assertEquals(0, saved.getUseCount());
        
        // 获取随机头像
        Optional<Avatar> randomAvatar = avatarService.getRandomAvatar();
        assertTrue(randomAvatar.isPresent());
        assertTrue(randomAvatar.get().getIsUsed());
        assertEquals(1, randomAvatar.get().getUseCount());
    }
}
