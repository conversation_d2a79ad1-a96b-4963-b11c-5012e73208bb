#!/bin/bash

echo "启动前端服务..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请先安装Node.js"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

# 检查前端目录是否存在
if [ ! -d "frontend-dist" ]; then
    echo "错误: 找不到frontend-dist目录"
    exit 1
fi

echo "正在启动前端服务..."
echo "前端服务将运行在: http://localhost:3004"
echo "按 Ctrl+C 停止服务"
echo

# 使用http-server启动静态文件服务
npx http-server frontend-dist -p 3004 -c-1 --cors
