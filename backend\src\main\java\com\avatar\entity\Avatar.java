package com.avatar.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 头像实体类
 */
@Entity
@Table(name = "avatars")
public class Avatar {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 原始文件名
     */
    @Column(name = "original_filename", nullable = false)
    private String originalFilename;
    
    /**
     * 存储的文件名（UUID生成）
     */
    @Column(name = "stored_filename", nullable = false, unique = true)
    private String storedFilename;
    
    /**
     * 文件路径
     */
    @Column(name = "file_path", nullable = false)
    private String filePath;
    
    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size")
    private Long fileSize;
    
    /**
     * 文件类型
     */
    @Column(name = "content_type")
    private String contentType;
    
    /**
     * 是否已被使用过
     */
    @Column(name = "is_used", nullable = false)
    private Boolean isUsed = false;
    
    /**
     * 使用次数
     */
    @Column(name = "use_count", nullable = false)
    private Integer useCount = 0;
    
    /**
     * 上传时间
     */
    @Column(name = "upload_time", nullable = false)
    private LocalDateTime uploadTime;
    
    /**
     * 最后使用时间
     */
    @Column(name = "last_used_time")
    private LocalDateTime lastUsedTime;

    // 构造函数
    public Avatar() {
        this.uploadTime = LocalDateTime.now();
    }

    public Avatar(String originalFilename, String storedFilename, String filePath, 
                  Long fileSize, String contentType) {
        this();
        this.originalFilename = originalFilename;
        this.storedFilename = storedFilename;
        this.filePath = filePath;
        this.fileSize = fileSize;
        this.contentType = contentType;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public String getStoredFilename() {
        return storedFilename;
    }

    public void setStoredFilename(String storedFilename) {
        this.storedFilename = storedFilename;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Boolean getIsUsed() {
        return isUsed;
    }

    public void setIsUsed(Boolean isUsed) {
        this.isUsed = isUsed;
    }

    public Integer getUseCount() {
        return useCount;
    }

    public void setUseCount(Integer useCount) {
        this.useCount = useCount;
    }

    public LocalDateTime getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }

    public LocalDateTime getLastUsedTime() {
        return lastUsedTime;
    }

    public void setLastUsedTime(LocalDateTime lastUsedTime) {
        this.lastUsedTime = lastUsedTime;
    }

    /**
     * 标记为已使用
     */
    public void markAsUsed() {
        this.isUsed = true;
        this.useCount++;
        this.lastUsedTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "Avatar{" +
                "id=" + id +
                ", originalFilename='" + originalFilename + '\'' +
                ", storedFilename='" + storedFilename + '\'' +
                ", isUsed=" + isUsed +
                ", useCount=" + useCount +
                '}';
    }
}
