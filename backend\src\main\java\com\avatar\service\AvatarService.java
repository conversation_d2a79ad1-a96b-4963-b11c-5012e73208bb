package com.avatar.service;

import com.avatar.entity.Avatar;
import com.avatar.repository.AvatarRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 头像业务服务
 */
@Service
public class AvatarService {
    
    @Autowired
    private AvatarRepository avatarRepository;
    
    @Autowired
    private FileStorageService fileStorageService;
    
    @Value("${avatar.upload-password}")
    private String uploadPassword;
    
    /**
     * 随机获取一个未使用的头像
     */
    public Optional<Avatar> getRandomAvatar() {
        Optional<Avatar> avatarOpt = avatarRepository.findRandomUnusedAvatar();
        
        if (avatarOpt.isPresent()) {
            Avatar avatar = avatarOpt.get();
            // 标记为已使用
            avatar.markAsUsed();
            avatarRepository.save(avatar);
            return Optional.of(avatar);
        }
        
        return Optional.empty();
    }
    
    /**
     * 批量上传头像
     */
    public List<Avatar> uploadAvatars(List<MultipartFile> files, String password) {
        // 验证密码
        if (!uploadPassword.equals(password)) {
            throw new IllegalArgumentException("密码错误");
        }
        
        List<Avatar> savedAvatars = new ArrayList<>();
        
        for (MultipartFile file : files) {
            try {
                Avatar avatar = uploadSingleAvatar(file);
                savedAvatars.add(avatar);
            } catch (Exception e) {
                // 记录错误但继续处理其他文件
                System.err.println("上传文件失败: " + file.getOriginalFilename() + ", 错误: " + e.getMessage());
            }
        }
        
        return savedAvatars;
    }
    
    /**
     * 上传单个头像
     */
    private Avatar uploadSingleAvatar(MultipartFile file) {
        // 存储文件
        String storedFilename = fileStorageService.storeFile(file);
        String filePath = fileStorageService.getFilePath(storedFilename).toString();
        
        // 创建头像记录
        Avatar avatar = new Avatar(
            file.getOriginalFilename(),
            storedFilename,
            filePath,
            file.getSize(),
            file.getContentType()
        );
        
        return avatarRepository.save(avatar);
    }
    
    /**
     * 根据文件名获取头像
     */
    public Optional<Avatar> getAvatarByFilename(String filename) {
        return avatarRepository.findByStoredFilename(filename);
    }
    
    /**
     * 获取头像统计信息
     */
    public AvatarStats getAvatarStats() {
        long totalCount = avatarRepository.count();
        long usedCount = avatarRepository.countByIsUsedTrue();
        long unusedCount = avatarRepository.countByIsUsedFalse();
        
        return new AvatarStats(totalCount, usedCount, unusedCount);
    }
    
    /**
     * 获取所有头像列表
     */
    public List<Avatar> getAllAvatars() {
        return avatarRepository.findAll();
    }
    
    /**
     * 重置头像使用状态（将所有头像标记为未使用）
     */
    public void resetAvatarUsage() {
        List<Avatar> allAvatars = avatarRepository.findAll();
        for (Avatar avatar : allAvatars) {
            avatar.setIsUsed(false);
            avatar.setUseCount(0);
            avatar.setLastUsedTime(null);
        }
        avatarRepository.saveAll(allAvatars);
    }

    /**
     * 同步文件系统中的图片到数据库
     */
    public Map<String, Object> syncImagesFromFileSystem() {
        Map<String, Object> result = new HashMap<>();
        AtomicInteger addedCount = new AtomicInteger(0);
        AtomicInteger skippedCount = new AtomicInteger(0);

        try {
            Path uploadsPath = Paths.get(fileStorageService.getStoragePath());
            if (!Files.exists(uploadsPath)) {
                result.put("addedCount", 0);
                result.put("skippedCount", 0);
                result.put("message", "uploads目录不存在");
                return result;
            }

            // 获取所有已存在的文件名
            Set<String> existingFilenames = avatarRepository.findAll()
                .stream()
                .map(Avatar::getStoredFilename)
                .collect(Collectors.toSet());

            // 遍历uploads目录中的所有图片文件
            Files.walk(uploadsPath, 1)
                .filter(Files::isRegularFile)
                .filter(path -> isImageFile(path.getFileName().toString()))
                .forEach(path -> {
                    String filename = path.getFileName().toString();
                    if (!existingFilenames.contains(filename)) {
                        try {
                            // 创建新的Avatar记录
                            Avatar avatar = new Avatar();
                            avatar.setStoredFilename(filename);
                            avatar.setOriginalFilename(filename);
                            avatar.setFilePath(path.toString());
                            avatar.setFileSize(Files.size(path));
                            avatar.setContentType(getContentTypeFromFilename(filename));
                            avatar.setIsUsed(false);
                            avatar.setUseCount(0);
                            avatar.setUploadTime(LocalDateTime.now());
                            avatarRepository.save(avatar);
                            addedCount.incrementAndGet();
                        } catch (Exception e) {
                            // 记录错误但继续处理其他文件
                            System.err.println("处理文件失败: " + filename + ", 错误: " + e.getMessage());
                        }
                    } else {
                        skippedCount.incrementAndGet();
                    }
                });

            result.put("addedCount", addedCount.get());
            result.put("skippedCount", skippedCount.get());
            result.put("message", String.format("同步完成：新增%d个，跳过%d个", addedCount.get(), skippedCount.get()));

        } catch (Exception e) {
            throw new RuntimeException("同步文件失败: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String filename) {
        String extension = filename.toLowerCase();
        return extension.endsWith(".jpg") ||
               extension.endsWith(".jpeg") ||
               extension.endsWith(".png") ||
               extension.endsWith(".gif");
    }

    /**
     * 根据文件名获取Content-Type
     */
    private String getContentTypeFromFilename(String filename) {
        String extension = filename.toLowerCase();
        if (extension.endsWith(".jpg") || extension.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (extension.endsWith(".png")) {
            return "image/png";
        } else if (extension.endsWith(".gif")) {
            return "image/gif";
        } else {
            return "application/octet-stream";
        }
    }
    
    /**
     * 头像统计信息内部类
     */
    public static class AvatarStats {
        private final long totalCount;
        private final long usedCount;
        private final long unusedCount;
        
        public AvatarStats(long totalCount, long usedCount, long unusedCount) {
            this.totalCount = totalCount;
            this.usedCount = usedCount;
            this.unusedCount = unusedCount;
        }
        
        public long getTotalCount() { return totalCount; }
        public long getUsedCount() { return usedCount; }
        public long getUnusedCount() { return unusedCount; }
    }
}
