@echo off
chcp 65001 >nul
echo 启动头像生成器后端服务...

REM 检查Java是否安装
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Java，请先安装Java 8或更高版本
    pause
    exit /b 1
)

REM 检查JAR文件是否存在
if not exist "avatar-generator-1.0.0.jar" (
    echo 错误: 找不到avatar-generator-1.0.0.jar文件
    pause
    exit /b 1
)

REM 创建必要的目录
if not exist "uploads" mkdir uploads
if not exist "data" mkdir data
if not exist "logs" mkdir logs

echo 正在启动服务，请稍候...
echo 服务启动后可访问: http://localhost:8081
echo 按 Ctrl+C 停止服务
echo.

java -jar avatar-generator-1.0.0.jar

pause
