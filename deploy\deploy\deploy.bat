@echo off
chcp 65001 >nul
echo ========================================
echo     头像生成器 - 一键部署脚本
echo ========================================
echo.

REM 检查Java是否安装
echo [1/4] 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Java，请先安装Java 8或更高版本
    echo 下载地址: https://www.oracle.com/java/technologies/javase-downloads.html
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

REM 检查JAR文件
echo [2/4] 检查应用文件...
if not exist "avatar-generator-1.0.0.jar" (
    echo ❌ 错误: 找不到avatar-generator-1.0.0.jar文件
    pause
    exit /b 1
)
echo ✅ 应用文件检查通过

REM 创建必要目录
echo [3/4] 初始化目录结构...
if not exist "uploads" mkdir uploads
if not exist "data" mkdir data
if not exist "logs" mkdir logs
echo ✅ 目录结构初始化完成

REM 启动服务
echo [4/4] 启动服务...
echo.
echo 🚀 正在启动头像生成器...
echo 📱 启动完成后请访问: http://localhost:8081
echo 🛑 按 Ctrl+C 可停止服务
echo.
echo ========================================
echo.

java -jar avatar-generator-1.0.0.jar

echo.
echo 服务已停止
pause
