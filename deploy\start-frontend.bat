@echo off
chcp 65001 >nul
echo 启动前端服务...

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查前端目录是否存在
if not exist "frontend-dist" (
    echo 错误: 找不到frontend-dist目录
    pause
    exit /b 1
)

echo 正在启动前端服务...
echo 前端服务将运行在: http://localhost:3004
echo 按 Ctrl+C 停止服务
echo.

REM 使用http-server启动静态文件服务
npx http-server frontend-dist -p 3004 -c-1 --cors

pause
