package com.avatar.repository;

import com.avatar.entity.Avatar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 头像数据访问层
 */
@Repository
public interface AvatarRepository extends JpaRepository<Avatar, Long> {
    
    /**
     * 根据存储文件名查找头像
     */
    Optional<Avatar> findByStoredFilename(String storedFilename);
    
    /**
     * 查找所有未使用的头像
     */
    List<Avatar> findByIsUsedFalse();
    
    /**
     * 查找所有已使用的头像
     */
    List<Avatar> findByIsUsedTrue();
    
    /**
     * 随机获取一个未使用的头像
     */
    @Query(value = "SELECT * FROM avatars WHERE is_used = false ORDER BY RANDOM() LIMIT 1", 
           nativeQuery = true)
    Optional<Avatar> findRandomUnusedAvatar();
    
    /**
     * 统计未使用的头像数量
     */
    long countByIsUsedFalse();
    
    /**
     * 统计已使用的头像数量
     */
    long countByIsUsedTrue();
    
    /**
     * 检查文件名是否已存在
     */
    boolean existsByStoredFilename(String storedFilename);
}
