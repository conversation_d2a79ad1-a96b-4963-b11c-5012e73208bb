#!/bin/bash

# 头像同步脚本 - 将uploads目录中的图片同步到数据库

echo "开始同步头像文件到数据库..."

UPLOADS_DIR="./uploads"
API_URL="http://localhost:8081/api/avatar/sync"

# 检查uploads目录是否存在
if [ ! -d "$UPLOADS_DIR" ]; then
    echo "错误: uploads目录不存在"
    exit 1
fi

# 检查后端服务是否运行
echo "检查后端服务状态..."
if ! curl -s "http://localhost:8081/api/avatar/stats" > /dev/null 2>&1; then
    echo "错误: 后端服务未运行，请先启动后端服务"
    echo "运行: ./start-backend.sh"
    exit 1
fi

# 调用同步API
echo "调用同步API..."
response=$(curl -s -X POST "$API_URL" -H "Content-Type: application/json")

if [ $? -eq 0 ]; then
    echo "同步完成！"
    echo "响应: $response"
else
    echo "同步失败，请检查后端服务状态"
fi

echo "同步脚本执行完成"
