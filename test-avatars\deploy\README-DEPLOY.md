# 🚀 头像生成器 - 宝塔部署包

## 📦 部署包说明

此部署包已经完成前后端打包，可直接部署到宝塔面板服务器。

### 包含文件：
```
deploy/
├── avatar-generator-1.0.0.jar    # 后端JAR包（已打包）
├── frontend-dist/                 # 前端静态文件（已打包）
│   ├── index.html
│   └── assets/
├── start-backend.sh              # 后端启动脚本
├── stop-backend.sh               # 后端停止脚本
├── nginx.conf                    # Nginx配置文件
├── data/                         # 数据库目录
├── uploads/                      # 头像上传目录
├── logs/                         # 日志目录
└── deploy.md                     # 详细部署说明
```

## ⚡ 快速部署

### 1. 上传到服务器
将整个 `deploy` 目录上传到服务器：
```bash
/www/wwwroot/avatar-generator/
```

### 2. 设置权限
```bash
chmod +x start-backend.sh stop-backend.sh
```

### 3. 配置Nginx
- 端口：3004
- 网站目录：`/www/wwwroot/avatar-generator/frontend-dist`
- 使用提供的 `nginx.conf` 配置

### 4. 启动服务
```bash
./start-backend.sh
```

## 🔧 配置信息

- **前端端口**: 3004
- **后端端口**: 8081  
- **上传密码**: 2217017462ab
- **访问地址**: http://你的域名:3004

## 📱 功能特性

✅ 随机生成不重复头像  
✅ 批量上传头像（密码保护）  
✅ 一键下载头像到本地/手机相册  
✅ 美观的响应式界面  
✅ H2文件数据库持久化存储  

## 📞 技术支持

如有部署问题，请查看 `deploy.md` 获取详细说明。
