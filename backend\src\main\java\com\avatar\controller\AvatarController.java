package com.avatar.controller;

import com.avatar.entity.Avatar;
import com.avatar.service.AvatarService;
import com.avatar.service.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 头像控制器
 */
@RestController
@RequestMapping("/api/avatar")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"})
public class AvatarController {

    private static final Logger logger = LoggerFactory.getLogger(AvatarController.class);

    @Autowired
    private AvatarService avatarService;
    
    @Autowired
    private FileStorageService fileStorageService;
    
    /**
     * 随机获取头像
     */
    @GetMapping("/random")
    public ResponseEntity<Map<String, Object>> getRandomAvatar() {
        try {
            Optional<Avatar> avatarOpt = avatarService.getRandomAvatar();
            
            if (avatarOpt.isPresent()) {
                Avatar avatar = avatarOpt.get();
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "获取头像成功");
                Map<String, Object> data = new HashMap<>();
                data.put("id", avatar.getId());
                data.put("filename", avatar.getStoredFilename());
                data.put("url", "/api/avatar/image/" + avatar.getStoredFilename());
                data.put("useCount", avatar.getUseCount());
                response.put("data", data);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "没有可用的头像");
                return ResponseEntity.ok(response);
            }
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取头像失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 批量上传头像
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadAvatars(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam("password") String password) {
        try {
            List<Avatar> savedAvatars = avatarService.uploadAvatars(files, password);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "上传成功");
            Map<String, Object> data = new HashMap<>();
            data.put("uploadedCount", savedAvatars.size());
            data.put("totalFiles", files.size());
            response.put("data", data);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "上传失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 获取头像图片
     */
    @GetMapping("/image/{filename}")
    public ResponseEntity<Resource> getAvatarImage(@PathVariable String filename) {
        try {
            Path filePath = fileStorageService.getFilePath(filename);
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists() && resource.isReadable()) {
                // 根据文件扩展名设置Content-Type
                String contentType = getContentType(filename);

                return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(resource);
            } else {
                logger.error("文件不存在或不可读: " + filename);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取图片失败: " + filename, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 下载头像图片
     */
    @GetMapping("/download/{filename}")
    public ResponseEntity<Resource> downloadAvatarImage(@PathVariable String filename) {
        try {
            Path filePath = fileStorageService.getFilePath(filename);
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists() && resource.isReadable()) {
                // 根据文件扩展名设置Content-Type
                String contentType = getContentType(filename);

                return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition")
                    .body(resource);
            } else {
                logger.error("文件不存在或不可读: " + filename);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("下载图片失败: " + filename, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取头像统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getAvatarStats() {
        try {
            AvatarService.AvatarStats stats = avatarService.getAvatarStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            Map<String, Object> data = new HashMap<>();
            data.put("totalCount", stats.getTotalCount());
            data.put("usedCount", stats.getUsedCount());
            data.put("unusedCount", stats.getUnusedCount());
            response.put("data", data);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 重置头像使用状态
     */
    @PostMapping("/reset")
    public ResponseEntity<Map<String, Object>> resetAvatarUsage(@RequestParam("password") String password) {
        try {
            avatarService.resetAvatarUsage();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "重置成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "重置失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 同步文件系统中的图片到数据库
     */
    @PostMapping("/sync")
    public ResponseEntity<Map<String, Object>> syncImages() {
        try {
            Map<String, Object> result = avatarService.syncImagesFromFileSystem();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "同步完成");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "同步失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 根据文件扩展名获取Content-Type
     */
    private String getContentType(String filename) {
        String extension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            default:
                return "application/octet-stream";
        }
    }
}
