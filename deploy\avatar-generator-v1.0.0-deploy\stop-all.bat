@echo off
chcp 65001 >nul
echo 正在停止头像生成器服务...

echo 停止后端服务（端口8081）...
for /f "tokens=5" %%i in ('netstat -ano ^| findstr :8081') do (
    echo 停止进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

echo 停止前端服务（端口3004）...
for /f "tokens=5" %%i in ('netstat -ano ^| findstr :3004') do (
    echo 停止进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

echo 停止Java进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo table /nh ^| findstr avatar-generator') do (
    echo 停止Java进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

echo 停止Node.js进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq node.exe" /fo table /nh ^| findstr http-server') do (
    echo 停止Node.js进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

echo ✅ 所有服务已停止
pause
