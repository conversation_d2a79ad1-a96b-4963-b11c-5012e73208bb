@echo off
chcp 65001 >nul
echo ========================================
echo     头像生成器 - 一键部署脚本
echo     前端端口: 3004  后端端口: 8081
echo ========================================
echo.

REM 检查Java是否安装
echo [1/5] 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Java，请先安装Java 8或更高版本
    echo 下载地址: https://www.oracle.com/java/technologies/javase-downloads.html
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

REM 检查Node.js是否安装
echo [2/5] 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

REM 检查应用文件
echo [3/5] 检查应用文件...
if not exist "avatar-generator-1.0.0.jar" (
    echo ❌ 错误: 找不到avatar-generator-1.0.0.jar文件
    pause
    exit /b 1
)
if not exist "frontend-dist" (
    echo ❌ 错误: 找不到frontend-dist目录
    pause
    exit /b 1
)
echo ✅ 应用文件检查通过

REM 创建必要目录
echo [4/5] 初始化目录结构...
if not exist "uploads" mkdir uploads
if not exist "data" mkdir data
if not exist "logs" mkdir logs
echo ✅ 目录结构初始化完成

REM 启动服务
echo [5/5] 启动服务...
echo.
echo 🚀 正在启动头像生成器...
echo 📱 前端地址: http://localhost:3004
echo 🔧 后端API: http://localhost:8081
echo 🛑 按 Ctrl+C 可停止服务
echo.
echo ========================================
echo.

echo 启动后端服务...
start "后端服务" java -jar avatar-generator-1.0.0.jar

echo 等待后端启动...
timeout /t 5 /nobreak >nul

echo 启动前端服务...
start "前端服务" npx http-server frontend-dist -p 3004 -c-1 --cors

echo.
echo ✅ 服务启动完成！
echo 📱 请访问: http://localhost:3004
echo.
pause
