@echo off
echo ========================================
echo Avatar Generator - Quick Start
echo Frontend: http://localhost:3004
echo Backend:  http://localhost:8081
echo ========================================
echo.

echo [1/4] Checking Java...
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java not found. Please install Java 8+
    pause
    exit /b 1
)
echo OK: Java found

echo [2/4] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found. Please install Node.js
    pause
    exit /b 1
)
echo OK: Node.js found

echo [3/4] Creating directories...
if not exist "uploads" mkdir uploads
if not exist "data" mkdir data
if not exist "logs" mkdir logs
echo OK: Directories created

echo [4/4] Starting services...
echo Starting backend service...
start "Backend" java -jar avatar-generator-1.0.0.jar

echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

echo Starting frontend service...
start "Frontend" npx http-server frontend-dist -p 3004 -c-1 --cors

echo.
echo ========================================
echo Services started successfully!
echo Frontend: http://localhost:3004
echo Backend:  http://localhost:8081
echo ========================================
pause
