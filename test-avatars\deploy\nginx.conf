# Nginx配置文件 - 头像生成器
# 适用于宝塔面板

server {
    listen 3004;
    server_name localhost;
    
    # 前端静态文件
    location / {
        root /www/wwwroot/avatar-generator/frontend/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 支持大文件上传
        client_max_body_size 10M;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /www/wwwroot/avatar-generator/frontend/dist;
    }
    
    # 日志配置
    access_log /www/wwwlogs/avatar-generator-access.log;
    error_log /www/wwwlogs/avatar-generator-error.log;
}
