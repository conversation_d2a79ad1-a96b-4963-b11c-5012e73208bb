#!/bin/bash

# 头像生成器后端启动脚本
# 适用于宝塔面板部署

echo "正在启动头像生成器后端服务..."

# 设置Java环境变量（根据实际情况调整）
export JAVA_HOME=/usr/local/java
export PATH=$JAVA_HOME/bin:$PATH

# 应用配置
APP_NAME="avatar-generator"
APP_VERSION="1.0.0"
JAR_FILE="avatar-generator-${APP_VERSION}.jar"
PID_FILE="./avatar-generator.pid"
LOG_FILE="./logs/avatar-generator.log"

# 创建必要的目录并设置权限
mkdir -p logs
mkdir -p data
mkdir -p uploads

# 设置目录权限
chmod 755 logs
chmod 755 data
chmod 777 uploads  # 上传目录需要写权限

echo "目录权限设置完成："
echo "- logs: 755"
echo "- data: 755"
echo "- uploads: 777 (可写)"

# 设置目录权限
chmod 755 logs
chmod 755 data
chmod 777 uploads  # 上传目录需要写权限

# 设置目录权限
chmod 755 logs
chmod 755 data
chmod 777 uploads  # 上传目录需要写权限

# 设置目录权限
chmod 755 logs
chmod 755 data
chmod 777 uploads  # 上传目录需要写权限

# 检查是否已经在运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "应用已经在运行，PID: $PID"
        exit 1
    else
        echo "删除过期的PID文件"
        rm -f $PID_FILE
    fi
fi

# 启动应用
echo "启动应用: $JAR_FILE"
nohup java -jar \
    -Xms512m \
    -Xmx1024m \
    -Dspring.profiles.active=prod \
    -Dserver.port=8081 \
    -Dfile.encoding=UTF-8 \
    $JAR_FILE > $LOG_FILE 2>&1 &

# 保存PID
echo $! > $PID_FILE

echo "应用启动成功！"
echo "PID: $(cat $PID_FILE)"
echo "日志文件: $LOG_FILE"
echo "访问地址: http://localhost:8081/api"

# 等待几秒检查启动状态
sleep 3
if ps -p $(cat $PID_FILE) > /dev/null 2>&1; then
    echo "应用运行正常"
else
    echo "应用启动失败，请检查日志文件"
    exit 1
fi
