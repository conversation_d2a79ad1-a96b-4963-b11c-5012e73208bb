@echo off
echo ========================================
echo Avatar Generator - Package Script
echo ========================================
echo.

set PACKAGE_NAME=avatar-generator-v1.0.0-deploy

echo [1/3] Preparing package directory...
if exist "%PACKAGE_NAME%" (
    echo Removing old package directory...
    rmdir /s /q "%PACKAGE_NAME%"
)
mkdir "%PACKAGE_NAME%"

echo [2/3] Copying files...
copy "avatar-generator-1.0.0.jar" "%PACKAGE_NAME%\"
copy "deploy.bat" "%PACKAGE_NAME%\"
copy "start-backend.bat" "%PACKAGE_NAME%\"
copy "stop-backend.bat" "%PACKAGE_NAME%\"
copy "sync-images.bat" "%PACKAGE_NAME%\"
copy "start-backend.sh" "%PACKAGE_NAME%\"
copy "stop-backend.sh" "%PACKAGE_NAME%\"
copy "sync-images.sh" "%PACKAGE_NAME%\"
copy "README.md" "%PACKAGE_NAME%\"
copy "VERSION.txt" "%PACKAGE_NAME%\"

echo Creating directory structure...
mkdir "%PACKAGE_NAME%\data"
mkdir "%PACKAGE_NAME%\uploads"
mkdir "%PACKAGE_NAME%\logs"

echo [3/3] Creating archive...
if exist "%PACKAGE_NAME%.zip" del "%PACKAGE_NAME%.zip"

echo Compressing files...
powershell -command "Compress-Archive -Path '%PACKAGE_NAME%' -DestinationPath '%PACKAGE_NAME%.zip'"

if exist "%PACKAGE_NAME%.zip" (
    echo Package created successfully!
    echo File location: %cd%\%PACKAGE_NAME%.zip
    echo Folder: %cd%\%PACKAGE_NAME%\
) else (
    echo Package creation failed!
)

echo.
echo ========================================
pause
