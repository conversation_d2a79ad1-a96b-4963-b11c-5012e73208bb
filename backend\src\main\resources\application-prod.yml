server:
  port: 8081
  servlet:
    context-path: /api

spring:
  application:
    name: avatar-generator
  
  # 数据库配置 (H2文件数据库)
  datasource:
    url: jdbc:h2:file:./data/avatardb;AUTO_SERVER=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: false
  
  # H2控制台配置
  h2:
    console:
      enabled: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# 自定义配置
avatar:
  # 上传密码
  upload-password: "2217017462ab"
  # 头像存储路径
  storage-path: "./uploads"
  # 允许的文件类型（逗号分隔）
  allowed-types: "image/jpeg,image/jpg,image/png,image/gif"

# 日志配置
logging:
  level:
    com.avatar: INFO
    org.springframework.web: WARN
  file:
    name: ./logs/avatar-generator.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
