# 头像生成器宝塔部署指南

## 📦 部署包内容

```
deploy/
├── avatar-generator-1.0.0.jar    # 后端JAR包
├── frontend-dist/                 # 前端打包文件
├── start-backend.sh              # 后端启动脚本
├── stop-backend.sh               # 后端停止脚本
├── nginx.conf                    # Nginx配置文件
└── deploy.md                     # 部署说明文档
```

## 🚀 部署步骤

### 1. 上传文件到服务器

将整个 `deploy` 目录上传到服务器，建议路径：
```bash
/www/wwwroot/avatar-generator/
```

### 2. 设置目录权限

```bash
cd /www/wwwroot/avatar-generator
chmod +x start-backend.sh
chmod +x stop-backend.sh
```

### 3. 配置Nginx

在宝塔面板中：
1. 进入"网站" -> "添加站点"
2. 域名填写你的域名或IP
3. 端口设置为 `3004`
4. 网站目录设置为 `/www/wwwroot/avatar-generator/frontend-dist`
5. 在"配置文件"中替换为提供的 `nginx.conf` 内容

### 4. 启动后端服务

```bash
cd /www/wwwroot/avatar-generator
./start-backend.sh
```

### 5. 检查服务状态

```bash
# 检查后端是否启动
curl http://localhost:8081/api/avatar/stats

# 检查前端是否可访问
curl http://localhost:3004
```

## 🔧 配置说明

### 端口配置
- **前端端口**: 3004
- **后端端口**: 8081
- **上传密码**: 2217017462ab

### 目录结构
```
/www/wwwroot/avatar-generator/
├── avatar-generator-1.0.0.jar
├── frontend-dist/              # 前端静态文件
├── data/                       # 数据库文件目录
├── uploads/                    # 头像上传目录
├── logs/                       # 日志文件目录
├── start-backend.sh
├── stop-backend.sh
└── avatar-generator.pid        # 进程ID文件
```

## 🛠️ 管理命令

### 启动服务
```bash
./start-backend.sh
```

### 停止服务
```bash
./stop-backend.sh
```

### 重启服务
```bash
./stop-backend.sh && ./start-backend.sh
```

### 查看日志
```bash
tail -f logs/avatar-generator.log
```

### 查看进程状态
```bash
ps aux | grep avatar-generator
```

## 🔍 故障排除

### 1. 后端启动失败
- 检查Java环境：`java -version`
- 查看日志：`cat logs/avatar-generator.log`
- 检查端口占用：`netstat -tlnp | grep 8081`

### 2. 前端无法访问
- 检查Nginx配置是否正确
- 确认前端文件是否上传完整
- 查看Nginx错误日志

### 3. 上传功能异常
- 确认 `uploads` 目录权限
- 检查文件大小限制
- 查看后端日志

## 📱 访问地址

部署成功后，可通过以下地址访问：
- **前端页面**: http://你的域名:3004
- **后端API**: http://你的域名:8081/api

## 🔐 安全建议

1. 修改默认上传密码
2. 配置防火墙规则
3. 定期备份数据库文件
4. 监控日志文件大小
