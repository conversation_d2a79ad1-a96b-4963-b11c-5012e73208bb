server:
  port: 8081

spring:
  application:
    name: avatar-generator
  
  # 数据库配置 (H2文件数据库 - 更稳定)
  datasource:
    url: jdbc:h2:file:./data/avatardb;AUTO_SERVER=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password:

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# 自定义配置
avatar:
  # 上传密码
  upload-password: "2217017462ab"
  # 头像存储路径
  storage-path: "./uploads"
  # 允许的文件类型（逗号分隔）
  allowed-types: "image/jpeg,image/jpg,image/png,image/gif"

# 跨域配置
cors:
  allowed-origins:
    - "http://localhost:3000"
    - "http://localhost:3004"
    - "http://localhost:5173"
    - "http://localhost:5174"
    - "*"
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers: "*"

# 日志配置
logging:
  level:
    com.avatar: DEBUG
    org.springframework.web: DEBUG
