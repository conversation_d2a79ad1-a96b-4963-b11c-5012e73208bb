package com.avatar.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件存储服务
 */
@Service
public class FileStorageService {
    
    @Value("${avatar.storage-path}")
    private String storagePath;
    
    @Value("${avatar.allowed-types}")
    private String allowedTypesStr;
    
    /**
     * 初始化存储目录
     */
    public void initStorageDirectory() {
        try {
            Path uploadPath = Paths.get(storagePath);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
        } catch (IOException e) {
            throw new RuntimeException("无法创建上传目录: " + storagePath, e);
        }
    }
    
    /**
     * 存储文件
     */
    public String storeFile(MultipartFile file) {
        // 验证文件
        validateFile(file);
        
        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        String storedFilename = UUID.randomUUID().toString() + fileExtension;
        
        try {
            // 确保存储目录存在
            initStorageDirectory();
            
            // 构建文件路径
            Path targetPath = Paths.get(storagePath).resolve(storedFilename);
            
            // 复制文件
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
            
            return storedFilename;
        } catch (IOException e) {
            throw new RuntimeException("文件存储失败: " + originalFilename, e);
        }
    }
    
    /**
     * 获取文件路径
     */
    public Path getFilePath(String filename) {
        return Paths.get(storagePath).resolve(filename);
    }
    
    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String filename) {
        return Files.exists(getFilePath(filename));
    }
    
    /**
     * 删除文件
     */
    public boolean deleteFile(String filename) {
        try {
            Path filePath = getFilePath(filename);
            return Files.deleteIfExists(filePath);
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        String contentType = file.getContentType();
        String[] allowedTypes = allowedTypesStr.split(",");
        boolean isAllowed = false;
        for (String type : allowedTypes) {
            if (type.trim().equals(contentType)) {
                isAllowed = true;
                break;
            }
        }
        if (contentType == null || !isAllowed) {
            throw new IllegalArgumentException("不支持的文件类型: " + contentType +
                ". 支持的类型: " + allowedTypesStr);
        }
        
        // 检查文件大小 (10MB)
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.'));
    }
    
    /**
     * 获取存储路径
     */
    public String getStoragePath() {
        return storagePath;
    }
}
