@echo off
chcp 65001 >nul
echo 开始同步头像文件到数据库...

set UPLOADS_DIR=.\uploads
set API_URL=http://localhost:8081/api/avatar/sync
set STATS_URL=http://localhost:8081/api/avatar/stats

REM 检查uploads目录是否存在
if not exist "%UPLOADS_DIR%" (
    echo 错误: uploads目录不存在
    pause
    exit /b 1
)

REM 检查后端服务是否运行
echo 检查后端服务状态...
curl -s "%STATS_URL%" >nul 2>&1
if errorlevel 1 (
    echo 错误: 后端服务未运行，请先启动后端服务
    echo 运行: start-backend.bat
    pause
    exit /b 1
)

REM 调用同步API
echo 调用同步API...
for /f "delims=" %%i in ('curl -s -X POST "%API_URL%" -H "Content-Type: application/json"') do set response=%%i

if errorlevel 0 (
    echo 同步完成！
    echo 响应: %response%
) else (
    echo 同步失败，请检查后端服务状态
)

echo 同步脚本执行完成
pause
