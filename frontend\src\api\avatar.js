import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 头像相关API
export const avatarAPI = {
  // 随机获取头像
  getRandomAvatar() {
    return api.get('/avatar/random')
  },

  // 批量上传头像
  uploadAvatars(files, password) {
    const formData = new FormData()
    formData.append('password', password)
    
    files.forEach(file => {
      formData.append('files', file)
    })

    return api.post('/avatar/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取头像统计信息
  getStats() {
    return api.get('/avatar/stats')
  },

  // 重置头像使用状态
  resetUsage(password) {
    return api.post('/avatar/reset', { password })
  },

  // 获取头像图片URL
  getImageUrl(filename) {
    return `/api/avatar/image/${filename}`
  }
}

export default api
