# 🚀 头像生成器 - 完整部署包

## 📦 部署包说明

此部署包已经完成前后端打包，包含所有必要文件，支持Windows和Linux系统。

### 📁 包含文件：
```
deploy/
├── avatar-generator-1.0.0.jar    # 后端JAR包（纯API服务）
├── frontend-dist/                # 前端静态文件
│   ├── index.html
│   └── assets/
├── deploy.bat                    # 一键部署脚本（Windows）
├── start-backend.bat             # 后端启动脚本（Windows）
├── start-frontend.bat            # 前端启动脚本（Windows）
├── stop-backend.bat              # 后端停止脚本（Windows）
├── stop-all.bat                  # 停止所有服务（Windows）
├── start-backend.sh              # 后端启动脚本（Linux/Mac）
├── start-frontend.sh             # 前端启动脚本（Linux/Mac）
├── stop-backend.sh               # 后端停止脚本（Linux/Mac）
├── sync-images.bat               # 图片同步脚本（Windows）
├── sync-images.sh                # 图片同步脚本（Linux/Mac）
├── data/                         # 数据库目录（自动创建）
├── uploads/                      # 头像存储目录（自动创建）
├── logs/                         # 日志目录（自动创建）
└── README.md                     # 部署说明
```

## ⚡ 快速部署

### 🖥️ Windows 系统

**方式一：一键部署（推荐）**
```cmd
双击运行 deploy.bat
```

**方式二：分步操作**
```cmd
# 启动服务
双击运行 start-backend.bat

# 停止服务
双击运行 stop-backend.bat
```

### 🐧 Linux/Mac 系统

```bash
# 给脚本执行权限
chmod +x *.sh

# 启动服务
./start-backend.sh

# 停止服务
./stop-backend.sh
```

### 🔧 手动启动

```bash
# 确保Java 8+已安装
java -version

# 启动应用
java -jar avatar-generator-1.0.0.jar
```

## 🌐 访问应用

启动成功后，打开浏览器访问：
```
前端地址: http://localhost:3004
后端API: http://localhost:8081
```

## 🎯 功能说明

- **🎲 随机生成头像** - 点击"生成头像"按钮
- **👁️ 预览头像** - 点击头像图片查看大图
- **📥 下载头像** - 点击"下载头像"按钮
- **📤 批量上传** - 点击"批量上传头像"（密码：2217017462ab）
- **📊 统计信息** - 查看头像使用统计

## 🔄 图片同步

如果手动添加图片到 `uploads` 目录，可以使用同步脚本：

**Windows:**
```cmd
sync-images.bat
```

**Linux/Mac:**
```bash
./sync-images.sh
```

## 📋 系统要求

- **Java**: 8 或更高版本（后端）
- **Node.js**: 14 或更高版本（前端）
- **内存**: 最少 512MB
- **磁盘**: 最少 100MB 可用空间
- **端口**: 3004（前端）和 8081（后端）端口需要可用

## 🔧 配置说明

应用使用以下默认配置：
- **前端端口**: 3004
- **后端端口**: 8081
- **数据库**: H2文件数据库（自动创建）
- **文件存储**: ./uploads 目录
- **上传密码**: 2217017462ab

## 🆘 常见问题

**Q: 启动失败，提示端口被占用？**
A: 运行stop-all.bat停止所有服务，或手动杀掉占用3004/8081端口的进程

**Q: 无法访问网页？**
A: 检查防火墙设置，确保3004和8081端口开放

**Q: 图片无法显示？**
A: 确保uploads目录有读写权限

**Q: 忘记上传密码？**
A: 默认密码是 `2217017462ab`

## 📞 技术支持

如有问题，请检查 `logs` 目录下的日志文件。
