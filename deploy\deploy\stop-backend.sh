#!/bin/bash

# 头像生成器后端停止脚本

echo "正在停止头像生成器后端服务..."

PID_FILE="./avatar-generator.pid"

if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "停止应用，PID: $PID"
        kill $PID
        
        # 等待进程结束
        for i in {1..30}; do
            if ! ps -p $PID > /dev/null 2>&1; then
                echo "应用已停止"
                rm -f $PID_FILE
                exit 0
            fi
            sleep 1
        done
        
        # 强制杀死进程
        echo "强制停止应用"
        kill -9 $PID
        rm -f $PID_FILE
        echo "应用已强制停止"
    else
        echo "应用未运行"
        rm -f $PID_FILE
    fi
else
    echo "PID文件不存在，应用可能未运行"
fi
