<template>
  <div id="app">
    <div class="container">
      <!-- 头部标题 -->
      <div class="header">
        <h1 class="title">
          <el-icon class="title-icon"><Picture /></el-icon>
          头像生成器
        </h1>
        <p class="subtitle">点击按钮随机生成独特头像，每个头像只能使用一次</p>
      </div>

      <!-- 主要功能区域 -->
      <div class="main-content">
        <!-- 头像显示区域 -->
        <div class="avatar-section">
          <div class="avatar-container" v-if="currentAvatar">
            <img
              :src="getFullImageUrl(currentAvatar.url)"
              :alt="currentAvatar.filename"
              class="avatar-image"
              @click="previewAvatar"
              title="点击预览大图"
            />
            <div class="avatar-info">
              <p>使用次数: {{ currentAvatar.useCount }}</p>
            </div>
            <!-- 下载按钮 -->
            <div class="download-section">
              <el-button
                type="success"
                @click="downloadAvatar"
                :icon="Download"
                class="download-btn">
                下载头像
              </el-button>
            </div>
          </div>
          <div class="avatar-placeholder" v-else>
            <el-icon class="placeholder-icon"><Picture /></el-icon>
            <p>点击下方按钮生成头像</p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button 
            type="primary" 
            size="large" 
            @click="generateAvatar"
            :loading="generating"
            class="generate-btn">
            <el-icon><Refresh /></el-icon>
            {{ generating ? '生成中...' : '生成头像' }}
          </el-button>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
          <el-card class="stats-card">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ stats.totalCount }}</div>
                <div class="stat-label">总头像数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ stats.usedCount }}</div>
                <div class="stat-label">已使用</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ stats.unusedCount }}</div>
                <div class="stat-label">可用头像</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 管理功能 -->
        <div class="admin-section">
          <el-button 
            type="warning" 
            @click="showUploadDialog = true"
            :icon="Upload">
            批量上传头像
          </el-button>
          <el-button 
            type="info" 
            @click="refreshStats"
            :icon="Refresh">
            刷新统计
          </el-button>
        </div>
      </div>

      <!-- 上传对话框 -->
      <el-dialog
        v-model="showUploadDialog"
        title="批量上传头像"
        width="500px"
        :before-close="handleUploadClose">
        <div class="upload-content">
          <el-form :model="uploadForm" label-width="80px">
            <el-form-item label="密码" required>
              <el-input
                v-model="uploadForm.password"
                type="password"
                placeholder="请输入上传密码"
                show-password />
            </el-form-item>
            <el-form-item label="选择文件" required>
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :multiple="true"
                :file-list="fileList"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                accept="image/*"
                :show-file-list="true"
                list-type="text"
                drag>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击选择文件</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 jpg/png/gif 格式，单个文件不超过10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleUploadClose">取消</el-button>
            <el-button 
              type="primary" 
              @click="handleUpload"
              :loading="uploading"
              :disabled="fileList.length === 0 || !uploadForm.password">
              {{ uploading ? '上传中...' : '确认上传' }}
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 头像预览对话框 -->
      <el-dialog
        v-model="showPreviewDialog"
        title="头像预览"
        width="600px"
        center>
        <div class="preview-container" v-if="currentAvatar">
          <img
            :src="getFullImageUrl(currentAvatar.url)"
            :alt="currentAvatar.filename"
            class="preview-image"
          />
          <div class="preview-info">
            <p><strong>文件名:</strong> {{ currentAvatar.filename }}</p>
            <p><strong>使用次数:</strong> {{ currentAvatar.useCount }}</p>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showPreviewDialog = false">关闭</el-button>
            <el-button
              type="success"
              @click="downloadAvatar"
              :icon="Download">
              下载头像
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, Refresh, Upload, UploadFilled, Download } from '@element-plus/icons-vue'
import axios from 'axios'

export default {
  name: 'App',
  components: {
    Picture,
    Refresh,
    Upload,
    UploadFilled,
    Download
  },
  setup() {
    // 响应式数据
    const currentAvatar = ref(null)
    const generating = ref(false)
    const showUploadDialog = ref(false)
    const showPreviewDialog = ref(false)
    const uploading = ref(false)
    const fileList = ref([])

    const stats = reactive({
      totalCount: 0,
      usedCount: 0,
      unusedCount: 0
    })
    
    const uploadForm = reactive({
      password: ''
    })

    // API基础URL - 根据环境选择
    const API_BASE = import.meta.env.DEV ? '/api' : 'http://*************:8081/api'

    // 生成头像
    const generateAvatar = async () => {
      generating.value = true
      try {
        const response = await axios.get(`${API_BASE}/avatar/random`)
        if (response.data.success) {
          currentAvatar.value = response.data.data
          ElMessage.success('头像生成成功！')
          await refreshStats()
        } else {
          ElMessage.warning(response.data.message || '没有可用的头像')
        }
      } catch (error) {
        ElMessage.error('生成头像失败：' + (error.response?.data?.message || error.message))
      } finally {
        generating.value = false
      }
    }

    // 刷新统计信息
    const refreshStats = async () => {
      try {
        const response = await axios.get(`${API_BASE}/avatar/stats`)
        if (response.data.success) {
          Object.assign(stats, response.data.data)
        }
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    }

    // 处理文件变化
    const handleFileChange = (file, uploadFileList) => {
      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      if (!allowedTypes.includes(file.raw.type)) {
        ElMessage.error('只支持 JPG、PNG、GIF 格式的图片')
        // 移除无效文件
        const index = uploadFileList.findIndex(f => f.uid === file.uid)
        if (index > -1) {
          uploadFileList.splice(index, 1)
        }
        return false
      }

      // 验证文件大小
      if (file.raw.size > 10 * 1024 * 1024) {
        ElMessage.error('文件大小不能超过 10MB')
        // 移除过大文件
        const index = uploadFileList.findIndex(f => f.uid === file.uid)
        if (index > -1) {
          uploadFileList.splice(index, 1)
        }
        return false
      }

      // 更新文件列表
      fileList.value = uploadFileList
    }

    // 处理文件移除
    const handleFileRemove = (file, uploadFileList) => {
      fileList.value = uploadFileList
    }

    // 处理上传
    const handleUpload = async () => {
      if (!uploadForm.password) {
        ElMessage.error('请输入密码')
        return
      }
      
      if (fileList.value.length === 0) {
        ElMessage.error('请选择要上传的文件')
        return
      }

      uploading.value = true
      try {
        const formData = new FormData()
        formData.append('password', uploadForm.password)
        
        fileList.value.forEach(file => {
          formData.append('files', file.raw)
        })

        const response = await axios.post(`${API_BASE}/avatar/upload`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response.data.success) {
          ElMessage.success(`上传成功！共上传 ${response.data.data.uploadedCount} 个文件`)
          handleUploadClose()
          await refreshStats()
        } else {
          ElMessage.error(response.data.message || '上传失败')
        }
      } catch (error) {
        ElMessage.error('上传失败：' + (error.response?.data?.message || error.message))
      } finally {
        uploading.value = false
      }
    }

    // 关闭上传对话框
    const handleUploadClose = () => {
      showUploadDialog.value = false
      uploadForm.password = ''
      fileList.value = []
    }

    // 预览头像
    const previewAvatar = () => {
      if (currentAvatar.value) {
        showPreviewDialog.value = true
      }
    }

    // 获取完整图片URL
    const getFullImageUrl = (url) => {
      if (!url) return ''
      // 如果已经是完整URL，直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url
      }
      // 在生产环境中，需要拼接服务器地址
      if (import.meta.env.DEV) {
        // 开发环境使用相对路径（通过Vite代理）
        return url
      } else {
        // 生产环境拼接完整服务器地址
        return `http://*************:8081${url}`
      }
    }

    // 下载头像
    const downloadAvatar = async () => {
      if (!currentAvatar.value) {
        ElMessage.warning('没有可下载的头像')
        return
      }

      try {
        // 生成文件名（使用时间戳确保唯一性）
        const timestamp = new Date().getTime()
        const extension = currentAvatar.value.filename.split('.').pop() || 'jpg'
        const filename = `avatar_${timestamp}.${extension}`

        // 直接使用图片URL进行下载
        const downloadUrl = currentAvatar.value.url

        // 创建临时下载链接
        const link = document.createElement('a')
        link.href = getFullImageUrl(downloadUrl)
        link.download = filename
        link.target = '_blank'

        // 设置下载属性
        link.setAttribute('download', filename)

        // 触发下载
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)

        ElMessage.success('头像下载成功！')

        // 如果是从预览对话框下载的，关闭对话框
        if (showPreviewDialog.value) {
          showPreviewDialog.value = false
        }
      } catch (error) {
        console.error('下载失败:', error)
        ElMessage.error('下载失败：' + (error.response?.data?.message || error.message))
      }
    }

    // 组件挂载时获取统计信息
    onMounted(() => {
      refreshStats()
    })

    return {
      currentAvatar,
      generating,
      showUploadDialog,
      showPreviewDialog,
      uploading,
      fileList,
      stats,
      uploadForm,
      generateAvatar,
      refreshStats,
      handleFileChange,
      handleFileRemove,
      handleUpload,
      handleUploadClose,
      previewAvatar,
      downloadAvatar,
      getFullImageUrl
    }
  }
}
</script>

<style scoped>
#app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.title {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.title-icon {
  font-size: 2.5rem;
}

.subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.main-content {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.avatar-section {
  text-align: center;
  margin-bottom: 30px;
}

.avatar-container {
  display: inline-block;
  position: relative;
}

.avatar-image {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  border: 5px solid #f0f0f0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.avatar-image:hover {
  transform: scale(1.05);
}

.avatar-info {
  margin-top: 15px;
  color: #666;
  font-size: 14px;
}

.download-section {
  margin-top: 20px;
  text-align: center;
}

.download-btn {
  border-radius: 20px;
  padding: 10px 30px;
  font-size: 14px;
  box-shadow: 0 3px 10px rgba(103, 194, 58, 0.3);
  transition: all 0.3s ease;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(103, 194, 58, 0.4);
}

/* 预览对话框样式 */
.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.preview-info {
  text-align: left;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
}

.preview-info p {
  margin: 8px 0;
  color: #666;
}

.avatar-placeholder {
  width: 200px;
  height: 200px;
  border: 3px dashed #ddd;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: #999;
  background: #fafafa;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 10px;
}

.action-buttons {
  text-align: center;
  margin-bottom: 30px;
}

.generate-btn {
  font-size: 1.1rem;
  padding: 15px 40px;
  border-radius: 25px;
  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
}

.stats-section {
  margin-bottom: 30px;
}

.stats-card {
  border-radius: 15px;
  border: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  text-align: center;
}

.stat-item {
  padding: 10px;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.admin-section {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.admin-section .el-button {
  margin: 0 10px;
  border-radius: 20px;
}

.upload-content {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .main-content {
    padding: 20px;
  }

  .title {
    font-size: 2rem;
  }

  .avatar-image,
  .avatar-placeholder {
    width: 150px;
    height: 150px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .admin-section .el-button {
    display: block;
    margin: 10px auto;
    width: 200px;
  }
}
</style>
