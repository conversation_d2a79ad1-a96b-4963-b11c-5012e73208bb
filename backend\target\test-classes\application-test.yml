spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
  
  h2:
    console:
      enabled: false

avatar:
  upload-password: "test123"
  storage-path: "./test-uploads"
  allowed-types: 
    - "image/jpeg"
    - "image/jpg" 
    - "image/png"
    - "image/gif"

logging:
  level:
    com.avatar: INFO
